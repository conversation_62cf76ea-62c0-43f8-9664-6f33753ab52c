package service

import (
	"errors"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
	"time"
)

// TaskService 任务服务层接口
type TaskService interface {
	// CreateTask 创建任务
	CreateTask(reportID, creditCode string) (uint, string, error)

	// GetTaskByID 根据ID获取任务详情
	GetTaskByID(id uint) (*model.Task, error)

	// ListTasksByUserID 根据用户ID查询任务列表
	ListTasksByUserID(userID uint, page, pageSize int) ([]*model.Task, int64, error)

	// ListTasksByTimeRange 根据时间区间查询任务列表
	ListTasksByTimeRange(startTime, endTime time.Time, page, pageSize int) ([]*model.Task, int64, error)

	// ListTasks 分页查询任务列表（支持多条件筛选）
	ListTasks(page, pageSize int, userID uint, status model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error)

	// UpdateTaskStatus 更新任务状态
	UpdateTaskStatus(id uint, status model.TaskStatus) error
}

// CreateTaskResult 创建任务结果
type CreateTaskResult struct {
	UserID   uint   `json:"user_id"`
	Password string `json:"password"`
	Code     string `json:"code"`
	Msg      string `json:"msg"`
}

// taskService 任务服务层实现
type taskService struct {
	taskDao dao.TaskDao
	userS   UserService
}

// NewTaskService 创建任务服务层实例
func NewTaskService(taskDao dao.TaskDao, userS UserService) TaskService {
	return &taskService{
		taskDao: taskDao,
		userS:   userS,
	}
}

// CreateTask 创建任务
func (s *taskService) CreateTask(reportID, creditCode string) (uint, string, error) {
	// 校验reportID是否已被创建
	existingTask, err := s.taskDao.GetByReportID(reportID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, "", err
	}
	if existingTask != nil {
		return 0, "", utils.ErrTaskReportIDExist
	}

	userID, password, err := s.userS.InitByUsername(creditCode, creditCode)
	if err != nil {
		return 0, "", err
	}

	// 创建任务
	task := &model.Task{
		UserID:   userID,
		Status:   model.TaskStatusPending,
		ReportID: reportID,
	}

	err = s.taskDao.Create(task)
	if err != nil {
		return 0, "", utils.NewAppError(utils.ErrInternalCode, "创建任务失败")
	}

	return userID, password, nil
}

// GetTaskByID 根据ID获取任务详情
func (s *taskService) GetTaskByID(id uint) (*model.Task, error) {
	if id == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "请填写任务ID")
	}
	task, err := s.taskDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.NewAppError(utils.ErrNotFoundCode, "任务不存在")
		}
		return nil, err
	}
	return task, nil
}

// ListTasksByUserID 根据用户ID查询任务列表
func (s *taskService) ListTasksByUserID(userID uint, page, pageSize int) ([]*model.Task, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	return s.taskDao.ListByUserID(userID, page, pageSize)
}

// ListTasksByTimeRange 根据时间区间查询任务列表
func (s *taskService) ListTasksByTimeRange(startTime, endTime time.Time, page, pageSize int) ([]*model.Task, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	return s.taskDao.ListByTimeRange(startTime, endTime, page, pageSize)
}

// ListTasks 分页查询任务列表（支持多条件筛选）
func (s *taskService) ListTasks(page, pageSize int, userID uint, status model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	list, i, err := s.taskDao.List(page, pageSize, userID, status, startTime, endTime)
	if err != nil {
		return nil, 0, err
	}

	return list, i, nil
}

// UpdateTaskStatus 更新任务状态
func (s *taskService) UpdateTaskStatus(id uint, status model.TaskStatus) error {
	task, err := s.taskDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrNotFoundCode, "任务不存在")
		}
		return err
	}

	// 如果状态相同，直接返回
	if task.Status == status {
		return nil
	}

	// 验证状态流转是否合法
	if !task.CanTransitionTo(status) {
		return utils.NewAppError(utils.ErrTaskStatusTransitionCode, "任务状态错误")
	}

	err = s.taskDao.Update(id, status)
	if err != nil {
		return err
	}

	return nil
}
